<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessHourResource;
use App\Models\Business;
use App\Models\BusinessHour;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BusinessHoursController extends Controller
{
    /**
     * Get business hours for a specific business owned by the user.
     */
    public function index(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $hours = $business->hours()
            ->orderBy('day_of_week')
            ->get();

        return response()->json([
            'data' => BusinessHourResource::collection($hours)
        ]);
    }

    /**
     * Update business hours for a business.
     */
    public function update(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $request->validate([
            'hours' => ['required', 'array', 'size:7'],
            'hours.*.day_of_week' => ['required', 'integer', 'between:1,7'],
            'hours.*.open_time' => ['nullable', 'date_format:H:i'],
            'hours.*.close_time' => ['nullable', 'date_format:H:i'],
            'hours.*.is_closed' => ['boolean'],
        ]);

        DB::beginTransaction();
        try {
            // Delete existing hours
            $business->hours()->delete();

            // Create new hours
            foreach ($request->hours as $hourData) {
                BusinessHour::create([
                    'business_id' => $business->id,
                    'day_of_week' => $hourData['day_of_week'],
                    'open_time' => $hourData['is_closed'] ? null : $hourData['open_time'],
                    'close_time' => $hourData['is_closed'] ? null : $hourData['close_time'],
                    'is_closed' => $hourData['is_closed'] ?? false,
                ]);
            }

            DB::commit();

            $hours = $business->hours()
                ->orderBy('day_of_week')
                ->get();

            return response()->json([
                'data' => BusinessHourResource::collection($hours)
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to update business hours'
            ], 500);
        }
    }

    /**
     * Update a specific day's hours.
     */
    public function updateDay(Request $request, Business $business, int $dayOfWeek): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        if ($dayOfWeek < 1 || $dayOfWeek > 7) {
            return response()->json([
                'message' => 'Invalid day of week'
            ], 400);
        }

        $request->validate([
            'open_time' => ['nullable', 'date_format:H:i'],
            'close_time' => ['nullable', 'date_format:H:i'],
            'is_closed' => ['boolean'],
        ]);

        $hour = BusinessHour::updateOrCreate(
            [
                'business_id' => $business->id,
                'day_of_week' => $dayOfWeek,
            ],
            [
                'open_time' => $request->boolean('is_closed') ? null : $request->open_time,
                'close_time' => $request->boolean('is_closed') ? null : $request->close_time,
                'is_closed' => $request->boolean('is_closed', false),
            ]
        );

        return response()->json([
            'data' => new BusinessHourResource($hour)
        ]);
    }
}
