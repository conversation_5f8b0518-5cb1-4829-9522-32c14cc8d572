<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Business;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BusinessAnalyticsController extends Controller
{
    /**
     * Get analytics overview for a specific business.
     */
    public function overview(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $analytics = [
            'total_reviews' => $business->reviews()->count(),
            'average_rating' => round($business->average_rating, 2),
            'total_favorites' => $business->favoritedBy()->count(),
            'total_products' => $business->products()->count(),
            'total_services' => $business->services()->count(),
            'total_media' => $business->media()->count(),
        ];

        return response()->json([
            'data' => $analytics
        ]);
    }

    /**
     * Get reviews analytics for a business.
     */
    public function reviews(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $period = $request->get('period', '30'); // days

        $startDate = Carbon::now()->subDays($period);

        $reviewsData = [
            'total_reviews' => $business->reviews()->count(),
            'recent_reviews' => $business->reviews()
                ->where('created_at', '>=', $startDate)
                ->count(),
            'average_rating' => round($business->average_rating, 2),
            'rating_distribution' => $business->reviews()
                ->select('rating', DB::raw('count(*) as count'))
                ->groupBy('rating')
                ->orderBy('rating', 'desc')
                ->get()
                ->pluck('count', 'rating'),
            'reviews_by_month' => $business->reviews()
                ->select(
                    DB::raw('YEAR(created_at) as year'),
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('count(*) as count')
                )
                ->where('created_at', '>=', Carbon::now()->subMonths(12))
                ->groupBy('year', 'month')
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->get(),
        ];

        return response()->json([
            'data' => $reviewsData
        ]);
    }

    /**
     * Get customer engagement analytics.
     */
    public function engagement(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $period = $request->get('period', '30'); // days
        $startDate = Carbon::now()->subDays($period);

        $engagementData = [
            'total_favorites' => $business->favoritedBy()->count(),
            'recent_favorites' => $business->favoritedBy()
                ->wherePivot('created_at', '>=', $startDate)
                ->count(),
            'repeat_customers' => $business->reviews()
                ->select('user_id')
                ->groupBy('user_id')
                ->havingRaw('count(*) > 1')
                ->count(),
            'response_rate' => $this->calculateResponseRate($business),
        ];

        return response()->json([
            'data' => $engagementData
        ]);
    }

    /**
     * Get products/services performance analytics.
     */
    public function offerings(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $offeringsData = [
            'products' => [
                'total' => $business->products()->count(),
                'available' => $business->products()->where('is_available', true)->count(),
                'categories' => $business->products()
                    ->whereNotNull('category')
                    ->select('category', DB::raw('count(*) as count'))
                    ->groupBy('category')
                    ->get()
                    ->pluck('count', 'category'),
            ],
            'services' => [
                'total' => $business->services()->count(),
                'available' => $business->services()->where('is_available', true)->count(),
                'categories' => $business->services()
                    ->whereNotNull('category')
                    ->select('category', DB::raw('count(*) as count'))
                    ->groupBy('category')
                    ->get()
                    ->pluck('count', 'category'),
            ],
        ];

        return response()->json([
            'data' => $offeringsData
        ]);
    }

    /**
     * Calculate the response rate for reviews.
     */
    private function calculateResponseRate(Business $business): float
    {
        $totalReviews = $business->reviews()->count();
        
        if ($totalReviews === 0) {
            return 0;
        }

        $repliedReviews = $business->reviews()
            ->whereNotNull('owner_reply')
            ->count();

        return round(($repliedReviews / $totalReviews) * 100, 2);
    }
}
