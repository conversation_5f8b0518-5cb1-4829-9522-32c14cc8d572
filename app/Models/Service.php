<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Sluggable\HasSlug;
use <PERSON>tie\Sluggable\SlugOptions;

class Service extends Model
{
    use HasFactory, HasSlug;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'name',
        'slug',
        'description',
        'price_from',
        'price_to',
        'duration_minutes',
        'category',
        'image_url',
        'is_available',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price_from' => 'decimal:2',
        'price_to' => 'decimal:2',
        'duration_minutes' => 'integer',
        'is_available' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }

    /**
     * Get the business that owns the service.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }
}
