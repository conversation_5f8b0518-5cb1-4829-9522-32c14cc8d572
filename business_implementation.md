# Business Owner Application - Complete Implementation Guide

## Overview
This document provides a comprehensive implementation guide for the Business Owner mobile application that allows business owners to register, manage multiple businesses, and track analytics through the Local Directory API.

## API Base Information
- **Base URL**: `http://127.0.0.1:8000/api/v1`
- **Authentication**: <PERSON><PERSON> Sanctum (Bearer Token)
- **Response Format**: JSON with `data` wrapper
- **Database**: MySQL (local_directory_api)

## Design System & UI/UX Specifications

### Color Palette
```
Primary Colors:
- Primary Blue: #2563EB (rgb(37, 99, 235))
- Primary Dark: #1D4ED8 (rgb(29, 78, 216))
- Primary Light: #DBEAFE (rgb(219, 234, 254))

Secondary Colors:
- Success Green: #10B981 (rgb(16, 185, 129))
- Warning Orange: #F59E0B (rgb(245, 158, 11))
- Error Red: #EF4444 (rgb(239, 68, 68))
- Info Blue: #3B82F6 (rgb(59, 130, 246))

Neutral Colors:
- Gray 900: #111827 (rgb(17, 24, 39)) - Primary Text
- Gray 700: #374151 (rgb(55, 65, 81)) - Secondary Text
- Gray 500: #6B7280 (rgb(107, 114, 128)) - Placeholder Text
- Gray 300: #D1D5DB (rgb(209, 213, 219)) - Borders
- Gray 100: #F3F4F6 (rgb(243, 244, 246)) - Background
- White: #FFFFFF (rgb(255, 255, 255))
```

### Typography
```
Font Family: Inter (fallback: system-ui, sans-serif)

Heading Styles:
- H1: 32px, Bold (700), Line Height 1.2
- H2: 28px, Bold (700), Line Height 1.3
- H3: 24px, SemiBold (600), Line Height 1.4
- H4: 20px, SemiBold (600), Line Height 1.4
- H5: 18px, Medium (500), Line Height 1.5

Body Styles:
- Body Large: 18px, Regular (400), Line Height 1.6
- Body Medium: 16px, Regular (400), Line Height 1.5
- Body Small: 14px, Regular (400), Line Height 1.4
- Caption: 12px, Regular (400), Line Height 1.3

Button Text: 16px, SemiBold (600), Line Height 1
```

### Spacing System
```
Base Unit: 4px

Spacing Scale:
- xs: 4px
- sm: 8px
- md: 16px
- lg: 24px
- xl: 32px
- 2xl: 48px
- 3xl: 64px

Component Spacing:
- Screen Padding: 16px (md)
- Card Padding: 16px (md)
- Section Spacing: 24px (lg)
- Element Spacing: 8px (sm)
```

### Component Specifications

#### Buttons
```
Primary Button:
- Background: Primary Blue (#2563EB)
- Text: White
- Height: 48px
- Border Radius: 8px
- Padding: 12px 24px
- Font: 16px SemiBold
- Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)

Secondary Button:
- Background: White
- Text: Primary Blue
- Border: 1px solid Primary Blue
- Height: 48px
- Border Radius: 8px
- Padding: 12px 24px
- Font: 16px SemiBold

Danger Button:
- Background: Error Red (#EF4444)
- Text: White
- Height: 48px
- Border Radius: 8px
- Padding: 12px 24px
- Font: 16px SemiBold
```

#### Input Fields
```
Text Input:
- Background: White
- Border: 1px solid Gray 300
- Border Radius: 8px
- Height: 48px
- Padding: 12px 16px
- Font: 16px Regular
- Placeholder: Gray 500

Focus State:
- Border: 2px solid Primary Blue
- Shadow: 0 0 0 3px rgba(37, 99, 235, 0.1)

Error State:
- Border: 2px solid Error Red
- Shadow: 0 0 0 3px rgba(239, 68, 68, 0.1)
```

#### Cards
```
Standard Card:
- Background: White
- Border Radius: 12px
- Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
- Padding: 16px

Elevated Card:
- Background: White
- Border Radius: 12px
- Shadow: 0 4px 6px rgba(0, 0, 0, 0.1)
- Padding: 20px
```

## Screen-by-Screen Implementation

### 1. Authentication Flow

#### 1.1 Login Screen
**Layout:**
- Full-screen with centered content
- Logo at top (120px height)
- Form fields with 16px spacing
- Login button below form
- "Don't have an account?" link at bottom

**API Endpoint:** `POST /login`
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**UI Components:**
- Email input field (required, email validation)
- Password input field (required, min 8 characters)
- "Remember me" checkbox
- Primary login button
- "Forgot password?" link
- "Sign up" navigation link

#### 1.2 Registration Screen
**Layout:**
- Scrollable form with sections
- Progress indicator (optional)
- Form validation with real-time feedback

**API Endpoint:** `POST /register`
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123"
}
```

**UI Components:**
- Full name input
- Email input with validation
- Password input with strength indicator
- Confirm password input
- Terms & conditions checkbox
- Primary register button

### 2. Dashboard & Navigation

#### 2.1 Main Dashboard
**Layout:**
- Top navigation bar with user avatar
- Quick stats cards (4 metrics in 2x2 grid)
- Recent activity list
- Quick action buttons

**API Endpoints:**
- `GET /me` - User profile
- `GET /my-businesses` - Business list
- `GET /my-businesses/{id}/analytics/overview` - Quick stats

**UI Components:**
```
Stats Cards (2x2 Grid):
┌─────────────┬─────────────┐
│ Total       │ Avg Rating  │
│ Reviews     │ 4.8 ⭐      │
│ 127         │             │
└─────────────┼─────────────┤
│ Favorites   │ Active      │
│ 89          │ Businesses  │
│             │ 3           │
└─────────────┴─────────────┘
```

#### 2.2 Bottom Navigation
**Tabs:**
1. **Dashboard** (Home icon)
2. **Businesses** (Building icon)
3. **Analytics** (Chart icon)
4. **Profile** (User icon)

### 3. Business Management

#### 3.1 Business List Screen
**Layout:**
- Search bar at top
- Business cards in vertical list
- Floating action button for "Add Business"
- Pull-to-refresh functionality

**API Endpoint:** `GET /my-businesses`

**Business Card Design:**
```
┌─────────────────────────────────────┐
│ [Logo] Business Name        [⋮]     │
│        123 Main St                  │
│        ⭐ 4.5 (23 reviews)          │
│        📍 Active • 🕒 Open Now      │
└─────────────────────────────────────┘
```

#### 3.2 Add/Edit Business Screen
**Layout:**
- Tabbed interface (Basic Info, Location, Categories, Hours)
- Form validation with error states
- Image upload for logo
- Map integration for location

**API Endpoints:**
- `POST /my-businesses` - Create business
- `PUT /my-businesses/{id}` - Update business
- `GET /categories` - Get categories

**Form Sections:**

**Basic Information Tab:**
- Business name (required)
- Description (required, multiline)
- Phone number (required, formatted)
- Website URL (optional)
- Logo upload

**Location Tab:**
- Address input with autocomplete
- Map view with draggable pin
- Latitude/longitude (auto-filled)

**Categories Tab:**
- Hierarchical category selection
- Multiple category support
- Search functionality

**Hours Tab:**
- 7-day schedule grid
- Toggle for "Closed" days
- Time picker for open/close times

### 4. Products & Services Management

#### 4.1 Products List Screen
**Layout:**
- Segmented control (Products | Services)
- Search and filter options
- Grid or list view toggle
- Add button for new items

**API Endpoints:**
- `GET /my-businesses/{id}/products`
- `GET /my-businesses/{id}/services`

**Product/Service Card:**
```
┌─────────────────────────────────────┐
│ [Image] Product Name        [⋮]     │
│         $19.99                      │
│         Available • Category        │
│         Last updated: 2 days ago    │
└─────────────────────────────────────┘
```

#### 4.2 Add/Edit Product Screen
**Layout:**
- Image upload section
- Form fields with validation
- Availability toggle
- Category selection

**API Endpoints:**
- `POST /my-businesses/{id}/products`
- `PUT /my-businesses/{id}/products/{productId}`

**Form Fields:**
- Product name (required)
- Description (required, multiline)
- Price (required, currency input)
- Category (optional, dropdown)
- Image upload
- Availability toggle
- Sort order (optional)

#### 4.3 Add/Edit Service Screen
**Layout:**
- Similar to product screen
- Additional duration field
- Price range support

**API Endpoints:**
- `POST /my-businesses/{id}/services`
- `PUT /my-businesses/{id}/services/{serviceId}`

**Form Fields:**
- Service name (required)
- Description (required, multiline)
- Price from (optional, currency)
- Price to (optional, currency)
- Duration in minutes (optional)
- Category (optional, dropdown)
- Image upload
- Availability toggle
- Sort order (optional)

### 5. Analytics & Insights

#### 5.1 Analytics Dashboard
**Layout:**
- Business selector dropdown
- Time period selector (7d, 30d, 90d, 1y)
- Key metrics cards
- Charts and graphs
- Detailed breakdowns

**API Endpoints:**
- `GET /my-businesses/{id}/analytics/overview`
- `GET /my-businesses/{id}/analytics/reviews`
- `GET /my-businesses/{id}/analytics/engagement`
- `GET /my-businesses/{id}/analytics/offerings`

**Metrics Layout:**
```
┌─────────────────────────────────────┐
│ Business: [Dropdown ▼]              │
│ Period: [30 Days ▼]                 │
├─────────────────────────────────────┤
│ ┌─────────┬─────────┬─────────┐     │
│ │ Reviews │ Rating  │ Favs    │     │
│ │ 45 📈   │ 4.8 ⭐  │ 23 ❤️   │     │
│ └─────────┴─────────┴─────────┘     │
├─────────────────────────────────────┤
│ [Reviews Chart]                     │
├─────────────────────────────────────┤
│ [Rating Distribution]               │
└─────────────────────────────────────┘
```

#### 5.2 Reviews Analytics
**Layout:**
- Rating distribution chart
- Recent reviews list
- Response rate metrics
- Sentiment analysis (if available)

**Components:**
- Horizontal bar chart for rating distribution
- Timeline chart for reviews over time
- List of recent reviews with quick reply option
- Response rate percentage with trend

#### 5.3 Customer Engagement
**Layout:**
- Favorites trend chart
- Customer retention metrics
- Engagement statistics

**Metrics:**
- Total favorites
- Favorites growth rate
- Repeat customers
- Average response time

### 6. Review Management

#### 6.1 Reviews List Screen
**Layout:**
- Business selector
- Filter options (Rating, Date, Replied/Unreplied)
- Review cards with reply functionality

**API Endpoint:** `GET /my-businesses/{id}/reviews` (Note: This endpoint needs to be created)

**Review Card Design:**
```
┌─────────────────────────────────────┐
│ ⭐⭐⭐⭐⭐ John D. • 2 days ago      │
│ "Great service and friendly staff!" │
│                                     │
│ [Reply] [📧 Contact]                │
│ ─────────────────────────────────   │
│ Your reply: "Thank you for..."      │
└─────────────────────────────────────┘
```

#### 6.2 Reply to Review Screen
**Layout:**
- Original review display
- Reply text area
- Character counter
- Send button

**API Endpoint:** `POST /my-business/reviews/{reviewId}/reply`

### 7. Media Management

#### 7.1 Media Gallery Screen
**Layout:**
- Grid view of uploaded media
- Filter by type (Images/Videos)
- Upload button
- Media details on tap

**API Endpoint:** `GET /my-businesses/{id}/media` (Note: This endpoint needs to be created)

#### 7.2 Upload Media Screen
**Layout:**
- File picker/camera integration
- Preview of selected media
- Caption input
- Upload progress

**API Endpoint:** `POST /my-business/media`

### 8. Profile & Settings

#### 8.1 Profile Screen
**Layout:**
- User avatar and basic info
- Account settings options
- Business settings
- Support and help

**API Endpoint:** `GET /me`

**Settings Options:**
- Edit profile information
- Change password
- Notification preferences
- Privacy settings
- Help & support
- Logout

## User Flow Diagrams

### Business Registration Flow
```
Login/Register → Dashboard → Add Business → 
Basic Info → Location → Categories → Hours → 
Review & Submit → Business Created → Dashboard
```

### Product Management Flow
```
Dashboard → Businesses → Select Business → 
Products Tab → Add Product → Fill Details → 
Upload Image → Save → Product List
```

### Review Response Flow
```
Dashboard → Reviews Notification → Reviews List → 
Select Review → Read Review → Write Reply → 
Send Reply → Updated Review List
```

## Responsive Design Considerations

### Mobile-First Approach
- Design for 375px width minimum
- Touch targets minimum 44px
- Thumb-friendly navigation
- Swipe gestures support

### Tablet Adaptations
- Two-column layouts where appropriate
- Larger touch targets
- Enhanced navigation
- Split-screen support

### Accessibility Requirements
- WCAG 2.1 AA compliance
- Screen reader support
- High contrast mode
- Voice control support
- Keyboard navigation

## State Management
- Use Redux/Provider pattern for global state
- Local state for form inputs
- Persistent storage for user preferences
- Offline capability for critical features

## Error Handling
- Network error handling with retry
- Form validation with clear messages
- Loading states for all async operations
- Graceful degradation for offline mode

## Performance Optimization
- Image lazy loading
- API response caching
- Pagination for large lists
- Background sync for uploads

## API Integration Details

### Authentication Flow
```dart
// Login implementation
Future<AuthResult> login(String email, String password) async {
  final response = await http.post(
    Uri.parse('$baseUrl/login'),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({
      'email': email,
      'password': password,
    }),
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body)['data'];
    await storage.write(key: 'auth_token', value: data['token']);
    return AuthResult.success(User.fromJson(data['user']));
  }

  return AuthResult.failure('Invalid credentials');
}
```

### Business Management API Calls

#### Create Business
```dart
Future<Business> createBusiness(BusinessData businessData) async {
  final response = await authenticatedPost(
    '/my-businesses',
    body: {
      'name': businessData.name,
      'description': businessData.description,
      'phone_number': businessData.phoneNumber,
      'website_url': businessData.websiteUrl,
      'address': businessData.address,
      'latitude': businessData.latitude,
      'longitude': businessData.longitude,
      'logo_url': businessData.logoUrl,
      'category_ids': businessData.categoryIds,
    },
  );

  return Business.fromJson(response.data);
}
```

#### Update Business Hours
```dart
Future<List<BusinessHour>> updateBusinessHours(
  int businessId,
  List<BusinessHourData> hours
) async {
  final response = await authenticatedPut(
    '/my-businesses/$businessId/hours',
    body: {
      'hours': hours.map((h) => {
        'day_of_week': h.dayOfWeek,
        'open_time': h.openTime,
        'close_time': h.closeTime,
        'is_closed': h.isClosed,
      }).toList(),
    },
  );

  return (response.data as List)
      .map((json) => BusinessHour.fromJson(json))
      .toList();
}
```

### Analytics API Integration
```dart
Future<AnalyticsOverview> getAnalyticsOverview(int businessId) async {
  final response = await authenticatedGet(
    '/my-businesses/$businessId/analytics/overview'
  );

  return AnalyticsOverview.fromJson(response.data);
}

Future<ReviewsAnalytics> getReviewsAnalytics(
  int businessId,
  {int period = 30}
) async {
  final response = await authenticatedGet(
    '/my-businesses/$businessId/analytics/reviews?period=$period'
  );

  return ReviewsAnalytics.fromJson(response.data);
}
```

## Data Models

### Business Model
```dart
class Business {
  final int id;
  final String name;
  final String slug;
  final String description;
  final String phoneNumber;
  final String? websiteUrl;
  final String address;
  final double latitude;
  final double longitude;
  final String? logoUrl;
  final bool isActive;
  final double averageRating;
  final int reviewCount;
  final List<Category> categories;
  final List<BusinessHour> hours;
  final List<Product> products;
  final List<Service> services;
  final DateTime createdAt;
  final DateTime updatedAt;

  Business({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.phoneNumber,
    this.websiteUrl,
    required this.address,
    required this.latitude,
    required this.longitude,
    this.logoUrl,
    required this.isActive,
    required this.averageRating,
    required this.reviewCount,
    required this.categories,
    required this.hours,
    required this.products,
    required this.services,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Business.fromJson(Map<String, dynamic> json) {
    return Business(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      phoneNumber: json['phone_number'],
      websiteUrl: json['website_url'],
      address: json['address'],
      latitude: double.parse(json['latitude'].toString()),
      longitude: double.parse(json['longitude'].toString()),
      logoUrl: json['logo_url'],
      isActive: json['is_active'],
      averageRating: double.parse(json['average_rating'].toString()),
      reviewCount: json['review_count'],
      categories: (json['categories'] as List?)
          ?.map((c) => Category.fromJson(c))
          .toList() ?? [],
      hours: (json['hours'] as List?)
          ?.map((h) => BusinessHour.fromJson(h))
          .toList() ?? [],
      products: (json['products'] as List?)
          ?.map((p) => Product.fromJson(p))
          .toList() ?? [],
      services: (json['services'] as List?)
          ?.map((s) => Service.fromJson(s))
          .toList() ?? [],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }
}
```

### Product Model
```dart
class Product {
  final int id;
  final int businessId;
  final String name;
  final String slug;
  final String description;
  final double price;
  final String? category;
  final String? imageUrl;
  final bool isAvailable;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  Product({
    required this.id,
    required this.businessId,
    required this.name,
    required this.slug,
    required this.description,
    required this.price,
    this.category,
    this.imageUrl,
    required this.isAvailable,
    required this.sortOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      businessId: json['business_id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      price: double.parse(json['price'].toString()),
      category: json['category'],
      imageUrl: json['image_url'],
      isAvailable: json['is_available'],
      sortOrder: json['sort_order'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }
}
```

### Analytics Models
```dart
class AnalyticsOverview {
  final int totalReviews;
  final double averageRating;
  final int totalFavorites;
  final int totalProducts;
  final int totalServices;
  final int totalMedia;

  AnalyticsOverview({
    required this.totalReviews,
    required this.averageRating,
    required this.totalFavorites,
    required this.totalProducts,
    required this.totalServices,
    required this.totalMedia,
  });

  factory AnalyticsOverview.fromJson(Map<String, dynamic> json) {
    return AnalyticsOverview(
      totalReviews: json['total_reviews'],
      averageRating: double.parse(json['average_rating'].toString()),
      totalFavorites: json['total_favorites'],
      totalProducts: json['total_products'],
      totalServices: json['total_services'],
      totalMedia: json['total_media'],
    );
  }
}

class ReviewsAnalytics {
  final int totalReviews;
  final int recentReviews;
  final double averageRating;
  final Map<int, int> ratingDistribution;
  final List<MonthlyReviewData> reviewsByMonth;

  ReviewsAnalytics({
    required this.totalReviews,
    required this.recentReviews,
    required this.averageRating,
    required this.ratingDistribution,
    required this.reviewsByMonth,
  });

  factory ReviewsAnalytics.fromJson(Map<String, dynamic> json) {
    return ReviewsAnalytics(
      totalReviews: json['total_reviews'],
      recentReviews: json['recent_reviews'],
      averageRating: double.parse(json['average_rating'].toString()),
      ratingDistribution: Map<int, int>.from(json['rating_distribution']),
      reviewsByMonth: (json['reviews_by_month'] as List)
          .map((r) => MonthlyReviewData.fromJson(r))
          .toList(),
    );
  }
}
```

## Widget Examples

### Business Card Widget
```dart
class BusinessCard extends StatelessWidget {
  final Business business;
  final VoidCallback? onTap;

  const BusinessCard({
    Key? key,
    required this.business,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: business.logoUrl != null
                        ? NetworkImage(business.logoUrl!)
                        : null,
                    child: business.logoUrl == null
                        ? Icon(Icons.business)
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          business.name,
                          style: Theme.of(context).textTheme.titleMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          business.address,
                          style: Theme.of(context).textTheme.bodySmall,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      // Handle menu actions
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(value: 'edit', child: Text('Edit')),
                      PopupMenuItem(value: 'analytics', child: Text('Analytics')),
                      PopupMenuItem(value: 'delete', child: Text('Delete')),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.star, color: Colors.amber, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${business.averageRating.toStringAsFixed(1)} (${business.reviewCount} reviews)',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: business.isActive ? Colors.green : Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      business.isActive ? 'Active' : 'Inactive',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

### Analytics Chart Widget
```dart
class ReviewsChart extends StatelessWidget {
  final List<MonthlyReviewData> data;

  const ReviewsChart({Key? key, required this.data}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Reviews Over Time',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(show: true),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: true),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final index = value.toInt();
                          if (index >= 0 && index < data.length) {
                            return Text('${data[index].month}/${data[index].year}');
                          }
                          return const Text('');
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: data.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.count.toDouble());
                      }).toList(),
                      isCurved: true,
                      color: Theme.of(context).primaryColor,
                      barWidth: 3,
                      dotData: FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

## Testing Strategy

### Unit Tests
- API service methods
- Data model serialization
- Business logic functions
- Utility functions

### Widget Tests
- Individual widget rendering
- User interaction handling
- State management
- Form validation

### Integration Tests
- Complete user flows
- API integration
- Navigation flows
- Error handling scenarios

## Deployment Considerations

### App Store Requirements
- Privacy policy implementation
- Data handling compliance
- App store guidelines adherence
- Review process preparation

### Performance Monitoring
- Crash reporting (Firebase Crashlytics)
- Performance monitoring
- User analytics
- API response time tracking

### Security Measures
- API token secure storage
- Certificate pinning
- Data encryption
- Input validation
