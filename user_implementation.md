# User-Facing Application - Complete Implementation Guide

## Overview
This document provides a comprehensive implementation guide for the User-Facing mobile application that allows users to discover nearby businesses, search by various criteria, view detailed business profiles, and manage favorites through the Local Directory API.

## API Base Information
- **Base URL**: `http://127.0.0.1:8000/api/v1`
- **Authentication**: <PERSON><PERSON> (Bearer Token) - Optional for some features
- **Response Format**: JSON with `data` wrapper
- **Database**: MySQL (local_directory_api)

## Design System & UI/UX Specifications

### Color Palette
```
Primary Colors:
- Primary Purple: #7C3AED (rgb(124, 58, 237))
- Primary Dark: #5B21B6 (rgb(91, 33, 182))
- Primary Light: #EDE9FE (rgb(237, 233, 254))

Secondary Colors:
- Success Green: #10B981 (rgb(16, 185, 129))
- Warning Orange: #F59E0B (rgb(245, 158, 11))
- Error Red: #EF4444 (rgb(239, 68, 68))
- Info Blue: #3B82F6 (rgb(59, 130, 246))

Accent Colors:
- Gold Star: #FCD34D (rgb(252, 211, 77))
- Heart Red: #F87171 (rgb(248, 113, 113))
- Distance Blue: #60A5FA (rgb(96, 165, 250))

Neutral Colors:
- Gray 900: #111827 (rgb(17, 24, 39)) - Primary Text
- Gray 700: #374151 (rgb(55, 65, 81)) - Secondary Text
- Gray 500: #6B7280 (rgb(107, 114, 128)) - Placeholder Text
- Gray 300: #D1D5DB (rgb(209, 213, 219)) - Borders
- Gray 100: #F3F4F6 (rgb(243, 244, 246)) - Background
- White: #FFFFFF (rgb(255, 255, 255))
```

### Typography
```
Font Family: Inter (fallback: system-ui, sans-serif)

Heading Styles:
- H1: 28px, Bold (700), Line Height 1.2
- H2: 24px, Bold (700), Line Height 1.3
- H3: 20px, SemiBold (600), Line Height 1.4
- H4: 18px, SemiBold (600), Line Height 1.4
- H5: 16px, Medium (500), Line Height 1.5

Body Styles:
- Body Large: 16px, Regular (400), Line Height 1.6
- Body Medium: 14px, Regular (400), Line Height 1.5
- Body Small: 12px, Regular (400), Line Height 1.4
- Caption: 10px, Regular (400), Line Height 1.3

Button Text: 14px, SemiBold (600), Line Height 1
```

### Spacing System
```
Base Unit: 4px

Spacing Scale:
- xs: 4px
- sm: 8px
- md: 12px
- lg: 16px
- xl: 20px
- 2xl: 24px
- 3xl: 32px

Component Spacing:
- Screen Padding: 16px (lg)
- Card Padding: 12px (md)
- Section Spacing: 20px (xl)
- Element Spacing: 8px (sm)
```

### Component Specifications

#### Search Bar
```
Search Input:
- Background: White
- Border: 1px solid Gray 300
- Border Radius: 24px
- Height: 44px
- Padding: 12px 16px 12px 44px
- Font: 16px Regular
- Placeholder: Gray 500
- Search Icon: 20px, Gray 500, positioned left

Focus State:
- Border: 2px solid Primary Purple
- Shadow: 0 0 0 3px rgba(124, 58, 237, 0.1)
```

#### Filter Chips
```
Filter Chip:
- Background: Gray 100
- Border: 1px solid Gray 300
- Border Radius: 20px
- Height: 32px
- Padding: 6px 12px
- Font: 12px Medium
- Text: Gray 700

Active Filter Chip:
- Background: Primary Purple
- Border: 1px solid Primary Purple
- Text: White

Filter Chip with Count:
- Badge: Primary Purple background, White text
- Position: Top right corner
```

#### Business Cards
```
List View Card:
- Background: White
- Border Radius: 12px
- Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
- Padding: 12px
- Margin: 8px horizontal, 4px vertical

Grid View Card:
- Background: White
- Border Radius: 12px
- Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
- Aspect Ratio: 1:1.2
- Padding: 8px
```

## Screen-by-Screen Implementation

### 1. Onboarding & Authentication

#### 1.1 Welcome Screen
**Layout:**
- Full-screen illustration
- App logo and tagline
- Location permission request
- Get started button

**Features:**
- Animated illustrations
- Location services setup
- Optional user registration

#### 1.2 Location Permission Screen
**Layout:**
- Location icon and explanation
- Benefits of location access
- Allow/Skip buttons

**Implementation:**
```dart
Future<void> requestLocationPermission() async {
  final permission = await Geolocator.requestPermission();
  if (permission == LocationPermission.always || 
      permission == LocationPermission.whileInUse) {
    await getCurrentLocation();
  }
}
```

### 2. Home & Discovery

#### 2.1 Home Screen
**Layout:**
- Search bar at top
- Category chips (horizontal scroll)
- "Near You" section with business cards
- "Popular" section
- "Recently Viewed" section (if logged in)

**API Endpoints:**
- `GET /categories` - Category list
- `GET /businesses?lat={lat}&lng={lng}` - Nearby businesses

**UI Components:**
```
┌─────────────────────────────────────┐
│ 🔍 Search businesses...            │
├─────────────────────────────────────┤
│ [🍕] [🛒] [⚡] [🏥] [🎬] [➡️]      │
├─────────────────────────────────────┤
│ Near You (2.1 km)                  │
│ ┌─────────────────────────────────┐ │
│ │ [📷] Pizza Palace        ❤️    │ │
│ │      ⭐ 4.5 • $$ • 0.8km      │ │
│ │      Open until 11 PM          │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 2.2 Search Results Screen
**Layout:**
- Search query display
- Filter button and active filters
- Sort options (Distance, Rating, Price)
- List/Grid view toggle
- Business results with pagination

**API Endpoint:** 
```
GET /businesses?search={query}&category={category}&rating={rating}&lat={lat}&lng={lng}&radius={radius}&price_range={range}&open_now={bool}&page={page}
```

**Filter Options:**
- Categories (multi-select)
- Rating (1-5 stars)
- Distance (1km, 5km, 10km, 25km)
- Price Range ($, $$, $$$, $$$$)
- Open Now (toggle)
- Has Products (toggle)
- Has Services (toggle)

#### 2.3 Map View Screen
**Layout:**
- Full-screen map
- Business markers with clustering
- Current location indicator
- Search bar overlay
- Filter button
- List view toggle

**Implementation:**
```dart
class MapScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: CameraPosition(
              target: _currentLocation,
              zoom: 14.0,
            ),
            markers: _businessMarkers,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
          ),
          _buildSearchOverlay(),
          _buildFilterButton(),
          _buildListToggle(),
        ],
      ),
    );
  }
}
```

### 3. Business Discovery & Details

#### 3.1 Business Detail Screen
**Layout:**
- Hero image/gallery
- Business name and rating
- Quick actions (Call, Website, Directions, Favorite)
- Business information tabs
- Reviews section
- Products/Services section

**API Endpoints:**
- `GET /businesses/{slug}` - Business details
- `GET /businesses/{id}/products` - Business products
- `GET /businesses/{id}/services` - Business services
- `GET /businesses/{id}/offerings` - Combined products/services

**Screen Sections:**

**Hero Section:**
```
┌─────────────────────────────────────┐
│ [📷 Business Photo Gallery]        │
│                              [❤️]   │
├─────────────────────────────────────┤
│ Pizza Palace                        │
│ ⭐⭐⭐⭐⭐ 4.8 (127 reviews)        │
│ 📍 0.8 km • $$ • Italian           │
│ 🕒 Open until 11:00 PM             │
├─────────────────────────────────────┤
│ [📞] [🌐] [🗺️] [❤️]               │
└─────────────────────────────────────┘
```

**Information Tabs:**
- Overview (Description, Hours, Contact)
- Menu/Products (if available)
- Services (if available)
- Reviews
- Photos

#### 3.2 Business Hours Display
**Layout:**
- Current day highlighted
- Open/Closed status
- All week schedule
- Special hours notes

**Implementation:**
```dart
class BusinessHoursWidget extends StatelessWidget {
  final List<BusinessHour> hours;
  
  @override
  Widget build(BuildContext context) {
    final today = DateTime.now().weekday;
    
    return Column(
      children: hours.map((hour) {
        final isToday = hour.dayOfWeek == today;
        return Container(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: isToday ? Colors.purple.withOpacity(0.1) : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getDayName(hour.dayOfWeek),
                style: TextStyle(
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              Text(
                hour.isClosed 
                    ? 'Closed' 
                    : '${hour.openTime} - ${hour.closeTime}',
                style: TextStyle(
                  color: hour.isClosed ? Colors.red : Colors.green,
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
```

#### 3.3 Products/Services Display
**Layout:**
- Category tabs (if multiple categories)
- Grid or list view of items
- Item cards with image, name, price
- Quick view modal for details

**Product/Service Card:**
```
┌─────────────────────────────────────┐
│ [📷] Margherita Pizza               │
│      Fresh tomatoes, mozzarella...  │
│      $18.99                         │
│      ⭐ 4.7 (23 reviews)            │
└─────────────────────────────────────┘
```

### 4. Reviews & Ratings

#### 4.1 Reviews List Screen
**Layout:**
- Overall rating summary
- Rating distribution chart
- Sort options (Newest, Oldest, Highest, Lowest)
- Review cards with user info

**API Endpoint:** `GET /businesses/{id}/reviews` (Note: This endpoint needs to be created)

**Review Card Design:**
```
┌─────────────────────────────────────┐
│ [👤] John D. • ⭐⭐⭐⭐⭐ • 2 days │
│ "Amazing pizza and great service!   │
│ The staff was very friendly and..." │
│                                     │
│ 👍 12 people found this helpful     │
│ ─────────────────────────────────   │
│ 💬 Owner replied: "Thank you..."    │
└─────────────────────────────────────┘
```

#### 4.2 Write Review Screen (Authenticated Users)
**Layout:**
- Business info header
- Star rating selector
- Review text area
- Photo upload option
- Submit button

**API Endpoint:** `POST /businesses/{id}/reviews`

**Implementation:**
```dart
class WriteReviewScreen extends StatefulWidget {
  final Business business;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Write Review')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            _buildBusinessHeader(),
            SizedBox(height: 24),
            _buildRatingSelector(),
            SizedBox(height: 16),
            _buildReviewTextField(),
            SizedBox(height: 16),
            _buildPhotoUpload(),
            Spacer(),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }
}
```

### 5. Favorites & User Profile

#### 5.1 Favorites Screen
**Layout:**
- Search within favorites
- Sort options (Recently Added, Distance, Rating)
- Favorite business cards
- Empty state for no favorites

**API Endpoint:** `GET /me/favorites`

**Features:**
- Swipe to remove from favorites
- Quick actions (Call, Directions)
- Offline access to basic info

#### 5.2 User Profile Screen
**Layout:**
- User avatar and basic info
- Account settings
- App preferences
- Support options

**API Endpoint:** `GET /me`

**Profile Options:**
- Edit profile
- Notification settings
- Privacy preferences
- Location settings
- Help & support
- About the app

### 6. Search & Filtering

#### 6.1 Advanced Search Screen
**Layout:**
- Search input with suggestions
- Recent searches
- Popular searches
- Search by category

**Search Suggestions Implementation:**
```dart
class SearchSuggestions extends StatelessWidget {
  final String query;
  final Function(String) onSuggestionTap;
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<String>>(
      future: _getSearchSuggestions(query),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return CircularProgressIndicator();
        
        return ListView.builder(
          itemCount: snapshot.data!.length,
          itemBuilder: (context, index) {
            final suggestion = snapshot.data![index];
            return ListTile(
              leading: Icon(Icons.search),
              title: Text(suggestion),
              onTap: () => onSuggestionTap(suggestion),
            );
          },
        );
      },
    );
  }
}
```

#### 6.2 Filter Screen
**Layout:**
- Filter categories with expandable sections
- Range sliders for distance and price
- Toggle switches for boolean filters
- Apply/Clear buttons

**Filter Categories:**
- Business Categories
- Distance Range
- Price Range
- Rating Minimum
- Open Now
- Has Products/Services

### 7. Navigation & Bottom Tabs

#### 7.1 Bottom Navigation
**Tabs:**
1. **Discover** (Home icon) - Home screen
2. **Search** (Search icon) - Search screen
3. **Map** (Map icon) - Map view
4. **Favorites** (Heart icon) - Favorites list
5. **Profile** (User icon) - Profile screen

#### 7.2 Navigation Implementation
```dart
class MainNavigationScreen extends StatefulWidget {
  @override
  _MainNavigationScreenState createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    DiscoverScreen(),
    SearchScreen(),
    MapScreen(),
    FavoritesScreen(),
    ProfileScreen(),
  ];
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.explore),
            label: 'Discover',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.map),
            label: 'Map',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'Favorites',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}
```

## User Flow Diagrams

### Discovery Flow
```
App Launch → Location Permission → Home Screen →
Category Selection → Business List → Business Details →
Action (Call/Directions/Favorite)
```

### Search Flow
```
Home Screen → Search Bar → Enter Query →
Apply Filters → Results List → Business Details →
Save to Favorites
```

### Review Flow
```
Business Details → Reviews Tab → Write Review →
Rate Business → Add Comment → Submit Review →
Updated Business Rating
```

### Favorites Flow
```
Business Details → Favorite Button → Added to Favorites →
Favorites Screen → Manage Favorites → Quick Actions
```

## API Integration Details

### Business Discovery API
```dart
class BusinessService {
  static const String baseUrl = 'http://127.0.0.1:8000/api/v1';

  Future<BusinessListResponse> getBusinesses({
    String? search,
    String? category,
    double? lat,
    double? lng,
    double? radius,
    int? rating,
    String? priceRange,
    bool? openNow,
    bool? hasProducts,
    bool? hasServices,
    int page = 1,
  }) async {
    final queryParams = <String, String>{
      if (search != null) 'search': search,
      if (category != null) 'category': category,
      if (lat != null) 'lat': lat.toString(),
      if (lng != null) 'lng': lng.toString(),
      if (radius != null) 'radius': radius.toString(),
      if (rating != null) 'rating': rating.toString(),
      if (priceRange != null) 'price_range': priceRange,
      if (openNow != null) 'open_now': openNow.toString(),
      if (hasProducts != null) 'has_products': hasProducts.toString(),
      if (hasServices != null) 'has_services': hasServices.toString(),
      'page': page.toString(),
    };

    final uri = Uri.parse('$baseUrl/businesses').replace(
      queryParameters: queryParams,
    );

    final response = await http.get(uri);

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      return BusinessListResponse.fromJson(json);
    }

    throw Exception('Failed to load businesses');
  }

  Future<Business> getBusinessDetails(String slug) async {
    final response = await http.get(
      Uri.parse('$baseUrl/businesses/$slug'),
    );

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      return Business.fromJson(json['data']);
    }

    throw Exception('Failed to load business details');
  }

  Future<ProductServiceResponse> getBusinessOfferings(int businessId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/businesses/$businessId/offerings'),
    );

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      return ProductServiceResponse.fromJson(json['data']);
    }

    throw Exception('Failed to load business offerings');
  }
}
```

### Favorites Management
```dart
class FavoritesService {
  Future<FavoriteResponse> toggleFavorite(int businessId) async {
    final token = await _getAuthToken();

    final response = await http.post(
      Uri.parse('$baseUrl/businesses/$businessId/favorite'),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
    );

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      return FavoriteResponse.fromJson(json['data']);
    }

    throw Exception('Failed to toggle favorite');
  }

  Future<List<Business>> getFavorites({int page = 1}) async {
    final token = await _getAuthToken();

    final response = await http.get(
      Uri.parse('$baseUrl/me/favorites?page=$page'),
      headers: {
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      return (json['data'] as List)
          .map((business) => Business.fromJson(business))
          .toList();
    }

    throw Exception('Failed to load favorites');
  }
}
```

### Location Services
```dart
class LocationService {
  Future<Position?> getCurrentLocation() async {
    try {
      final permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        final requestedPermission = await Geolocator.requestPermission();
        if (requestedPermission == LocationPermission.denied) {
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return null;
      }

      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      print('Error getting location: $e');
      return null;
    }
  }

  double calculateDistance(
    double startLat,
    double startLng,
    double endLat,
    double endLng,
  ) {
    return Geolocator.distanceBetween(
      startLat,
      startLng,
      endLat,
      endLng,
    ) / 1000; // Convert to kilometers
  }

  Future<void> openMapsApp(double lat, double lng, String label) async {
    final url = 'https://www.google.com/maps/search/?api=1&query=$lat,$lng';
    if (await canLaunch(url)) {
      await launch(url);
    }
  }
}
```

## Data Models

### Business Model
```dart
class Business {
  final int id;
  final String name;
  final String slug;
  final String? logoUrl;
  final String address;
  final double latitude;
  final double longitude;
  final double averageRating;
  final int reviewCount;
  final double? distanceKm;
  final bool isActive;
  final String? description;
  final String? phoneNumber;
  final String? websiteUrl;
  final List<Category> categories;
  final List<BusinessHour> hours;
  final List<Review> reviews;
  final List<BusinessMedia> media;
  final List<Product> products;
  final List<Service> services;

  Business({
    required this.id,
    required this.name,
    required this.slug,
    this.logoUrl,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.averageRating,
    required this.reviewCount,
    this.distanceKm,
    required this.isActive,
    this.description,
    this.phoneNumber,
    this.websiteUrl,
    this.categories = const [],
    this.hours = const [],
    this.reviews = const [],
    this.media = const [],
    this.products = const [],
    this.services = const [],
  });

  factory Business.fromJson(Map<String, dynamic> json) {
    return Business(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      logoUrl: json['logo_url'],
      address: json['address'],
      latitude: double.parse(json['latitude'].toString()),
      longitude: double.parse(json['longitude'].toString()),
      averageRating: double.parse(json['average_rating'].toString()),
      reviewCount: json['review_count'],
      distanceKm: json['distance_km'] != null
          ? double.parse(json['distance_km'].toString())
          : null,
      isActive: json['is_active'],
      description: json['description'],
      phoneNumber: json['phone_number'],
      websiteUrl: json['website_url'],
      categories: (json['categories'] as List?)
          ?.map((c) => Category.fromJson(c))
          .toList() ?? [],
      hours: (json['hours'] as List?)
          ?.map((h) => BusinessHour.fromJson(h))
          .toList() ?? [],
      reviews: (json['reviews'] as List?)
          ?.map((r) => Review.fromJson(r))
          .toList() ?? [],
      media: (json['media'] as List?)
          ?.map((m) => BusinessMedia.fromJson(m))
          .toList() ?? [],
      products: (json['products'] as List?)
          ?.map((p) => Product.fromJson(p))
          .toList() ?? [],
      services: (json['services'] as List?)
          ?.map((s) => Service.fromJson(s))
          .toList() ?? [],
    );
  }

  bool get isOpenNow {
    final now = DateTime.now();
    final currentDay = now.weekday == 7 ? 7 : now.weekday; // Handle Sunday
    final currentTime = TimeOfDay.fromDateTime(now);

    final todayHours = hours.firstWhere(
      (h) => h.dayOfWeek == currentDay,
      orElse: () => BusinessHour(
        id: 0,
        businessId: id,
        dayOfWeek: currentDay,
        openTime: null,
        closeTime: null,
        isClosed: true,
      ),
    );

    if (todayHours.isClosed ||
        todayHours.openTime == null ||
        todayHours.closeTime == null) {
      return false;
    }

    final openTime = _parseTimeOfDay(todayHours.openTime!);
    final closeTime = _parseTimeOfDay(todayHours.closeTime!);

    return _isTimeBetween(currentTime, openTime, closeTime);
  }

  String get priceRange {
    if (products.isEmpty && services.isEmpty) return '';

    final prices = <double>[];

    for (final product in products) {
      prices.add(product.price);
    }

    for (final service in services) {
      if (service.priceFrom != null) prices.add(service.priceFrom!);
      if (service.priceTo != null) prices.add(service.priceTo!);
    }

    if (prices.isEmpty) return '';

    final minPrice = prices.reduce((a, b) => a < b ? a : b);
    final maxPrice = prices.reduce((a, b) => a > b ? a : b);

    if (maxPrice <= 15) return '\$';
    if (maxPrice <= 30) return '\$\$';
    if (maxPrice <= 60) return '\$\$\$';
    return '\$\$\$\$';
  }
}
```

### Category Model
```dart
class Category {
  final int id;
  final String name;
  final String slug;
  final String? iconUrl;
  final List<Category> children;

  Category({
    required this.id,
    required this.name,
    required this.slug,
    this.iconUrl,
    this.children = const [],
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      iconUrl: json['icon_url'],
      children: (json['children'] as List?)
          ?.map((c) => Category.fromJson(c))
          .toList() ?? [],
    );
  }
}
```

### Review Model
```dart
class Review {
  final int id;
  final int rating;
  final String comment;
  final String? ownerReply;
  final DateTime createdAt;
  final ReviewUser user;

  Review({
    required this.id,
    required this.rating,
    required this.comment,
    this.ownerReply,
    required this.createdAt,
    required this.user,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'],
      rating: json['rating'],
      comment: json['comment'],
      ownerReply: json['owner_reply'],
      createdAt: DateTime.parse(json['created_at']),
      user: ReviewUser.fromJson(json['user']),
    );
  }
}

class ReviewUser {
  final int id;
  final String name;

  ReviewUser({
    required this.id,
    required this.name,
  });

  factory ReviewUser.fromJson(Map<String, dynamic> json) {
    return ReviewUser(
      id: json['id'],
      name: json['name'],
    );
  }
}
```

### Product and Service Models
```dart
class Product {
  final int id;
  final String name;
  final String slug;
  final String description;
  final double price;
  final String? category;
  final String? imageUrl;
  final bool isAvailable;

  Product({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    required this.price,
    this.category,
    this.imageUrl,
    required this.isAvailable,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      price: double.parse(json['price'].toString()),
      category: json['category'],
      imageUrl: json['image_url'],
      isAvailable: json['is_available'],
    );
  }
}

class Service {
  final int id;
  final String name;
  final String slug;
  final String description;
  final double? priceFrom;
  final double? priceTo;
  final int? durationMinutes;
  final String? category;
  final String? imageUrl;
  final bool isAvailable;

  Service({
    required this.id,
    required this.name,
    required this.slug,
    required this.description,
    this.priceFrom,
    this.priceTo,
    this.durationMinutes,
    this.category,
    this.imageUrl,
    required this.isAvailable,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      priceFrom: json['price_from'] != null
          ? double.parse(json['price_from'].toString())
          : null,
      priceTo: json['price_to'] != null
          ? double.parse(json['price_to'].toString())
          : null,
      durationMinutes: json['duration_minutes'],
      category: json['category'],
      imageUrl: json['image_url'],
      isAvailable: json['is_available'],
    );
  }

  String get priceDisplay {
    if (priceFrom == null && priceTo == null) return 'Price on request';
    if (priceFrom != null && priceTo != null) {
      if (priceFrom == priceTo) return '\$${priceFrom!.toStringAsFixed(2)}';
      return '\$${priceFrom!.toStringAsFixed(2)} - \$${priceTo!.toStringAsFixed(2)}';
    }
    if (priceFrom != null) return 'From \$${priceFrom!.toStringAsFixed(2)}';
    return 'Up to \$${priceTo!.toStringAsFixed(2)}';
  }
}
```

## Widget Examples

### Business Card Widget
```dart
class BusinessCard extends StatelessWidget {
  final Business business;
  final VoidCallback? onTap;
  final bool showDistance;

  const BusinessCard({
    Key? key,
    required this.business,
    this.onTap,
    this.showDistance = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildBusinessImage(),
              const SizedBox(width: 12),
              Expanded(
                child: _buildBusinessInfo(),
              ),
              _buildFavoriteButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBusinessImage() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[200],
      ),
      child: business.logoUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                business.logoUrl!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(Icons.business, size: 40, color: Colors.grey);
                },
              ),
            )
          : Icon(Icons.business, size: 40, color: Colors.grey),
    );
  }

  Widget _buildBusinessInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          business.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            Icon(Icons.star, color: Colors.amber, size: 16),
            const SizedBox(width: 4),
            Text(
              '${business.averageRating.toStringAsFixed(1)} (${business.reviewCount})',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            if (business.priceRange.isNotEmpty) ...[
              const SizedBox(width: 8),
              Text(
                business.priceRange,
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ],
        ),
        const SizedBox(height: 4),
        Text(
          business.address,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            if (showDistance && business.distanceKm != null) ...[
              Icon(Icons.location_on, size: 12, color: Colors.blue),
              const SizedBox(width: 2),
              Text(
                '${business.distanceKm!.toStringAsFixed(1)} km',
                style: const TextStyle(fontSize: 12, color: Colors.blue),
              ),
              const SizedBox(width: 8),
            ],
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: business.isOpenNow ? Colors.green : Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                business.isOpenNow ? 'Open' : 'Closed',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFavoriteButton() {
    return IconButton(
      onPressed: () {
        // Handle favorite toggle
      },
      icon: Icon(
        Icons.favorite_border, // Use Icons.favorite when favorited
        color: Colors.grey,
      ),
    );
  }
}
```

### Category Chip Widget
```dart
class CategoryChip extends StatelessWidget {
  final Category category;
  final bool isSelected;
  final VoidCallback onTap;

  const CategoryChip({
    Key? key,
    required this.category,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (category.iconUrl != null) ...[
              Image.network(
                category.iconUrl!,
                width: 16,
                height: 16,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.category,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.grey[600],
                  );
                },
              ),
              const SizedBox(width: 6),
            ],
            Text(
              category.name,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[700],
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

### Search Bar Widget
```dart
class SearchBarWidget extends StatefulWidget {
  final String? initialQuery;
  final Function(String) onSearch;
  final VoidCallback? onFilterTap;
  final int? activeFiltersCount;

  const SearchBarWidget({
    Key? key,
    this.initialQuery,
    required this.onSearch,
    this.onFilterTap,
    this.activeFiltersCount,
  }) : super(key: key);

  @override
  _SearchBarWidgetState createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _controller,
                onSubmitted: widget.onSearch,
                decoration: InputDecoration(
                  hintText: 'Search businesses...',
                  prefixIcon: const Icon(Icons.search, color: Colors.grey),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
          ),
          if (widget.onFilterTap != null) ...[
            const SizedBox(width: 12),
            GestureDetector(
              onTap: widget.onFilterTap,
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.activeFiltersCount != null && widget.activeFiltersCount! > 0
                      ? Theme.of(context).primaryColor
                      : Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    Icon(
                      Icons.tune,
                      color: widget.activeFiltersCount != null && widget.activeFiltersCount! > 0
                          ? Colors.white
                          : Colors.grey[600],
                    ),
                    if (widget.activeFiltersCount != null && widget.activeFiltersCount! > 0)
                      Positioned(
                        right: -2,
                        top: -2,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 16,
                            minHeight: 16,
                          ),
                          child: Text(
                            '${widget.activeFiltersCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
```

## State Management

### Business State Management
```dart
class BusinessProvider extends ChangeNotifier {
  List<Business> _businesses = [];
  List<Category> _categories = [];
  bool _isLoading = false;
  String? _error;
  BusinessFilter _currentFilter = BusinessFilter();

  List<Business> get businesses => _businesses;
  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;
  String? get error => _error;
  BusinessFilter get currentFilter => _currentFilter;

  Future<void> loadBusinesses({bool refresh = false}) async {
    if (refresh) {
      _businesses.clear();
    }

    _setLoading(true);
    _setError(null);

    try {
      final response = await BusinessService().getBusinesses(
        search: _currentFilter.search,
        category: _currentFilter.category,
        lat: _currentFilter.latitude,
        lng: _currentFilter.longitude,
        radius: _currentFilter.radius,
        rating: _currentFilter.minRating,
        priceRange: _currentFilter.priceRange,
        openNow: _currentFilter.openNow,
        hasProducts: _currentFilter.hasProducts,
        hasServices: _currentFilter.hasServices,
        page: refresh ? 1 : (_businesses.length ~/ 15) + 1,
      );

      if (refresh) {
        _businesses = response.businesses;
      } else {
        _businesses.addAll(response.businesses);
      }

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  Future<void> loadCategories() async {
    try {
      _categories = await BusinessService().getCategories();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  void updateFilter(BusinessFilter filter) {
    _currentFilter = filter;
    loadBusinesses(refresh: true);
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }
}
```

## Testing Strategy

### Unit Tests
```dart
// test/models/business_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:your_app/models/business.dart';

void main() {
  group('Business Model Tests', () {
    test('should parse business from JSON correctly', () {
      final json = {
        'id': 1,
        'name': 'Test Business',
        'slug': 'test-business',
        'address': '123 Test St',
        'latitude': '40.7128',
        'longitude': '-74.0060',
        'average_rating': '4.5',
        'review_count': 10,
        'is_active': true,
      };

      final business = Business.fromJson(json);

      expect(business.id, 1);
      expect(business.name, 'Test Business');
      expect(business.latitude, 40.7128);
      expect(business.averageRating, 4.5);
      expect(business.isActive, true);
    });

    test('should calculate price range correctly', () {
      final business = Business(
        id: 1,
        name: 'Test',
        slug: 'test',
        address: 'Test Address',
        latitude: 0,
        longitude: 0,
        averageRating: 4.5,
        reviewCount: 10,
        isActive: true,
        products: [
          Product(
            id: 1,
            name: 'Product 1',
            slug: 'product-1',
            description: 'Test product',
            price: 25.0,
            isAvailable: true,
          ),
        ],
      );

      expect(business.priceRange, '\$\$');
    });
  });
}
```

### Widget Tests
```dart
// test/widgets/business_card_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:your_app/widgets/business_card.dart';
import 'package:your_app/models/business.dart';

void main() {
  group('BusinessCard Widget Tests', () {
    testWidgets('should display business information correctly', (tester) async {
      final business = Business(
        id: 1,
        name: 'Test Business',
        slug: 'test-business',
        address: '123 Test St',
        latitude: 40.7128,
        longitude: -74.0060,
        averageRating: 4.5,
        reviewCount: 10,
        isActive: true,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessCard(business: business),
          ),
        ),
      );

      expect(find.text('Test Business'), findsOneWidget);
      expect(find.text('123 Test St'), findsOneWidget);
      expect(find.text('4.5 (10)'), findsOneWidget);
    });

    testWidgets('should handle tap events', (tester) async {
      bool tapped = false;
      final business = Business(
        id: 1,
        name: 'Test Business',
        slug: 'test-business',
        address: '123 Test St',
        latitude: 40.7128,
        longitude: -74.0060,
        averageRating: 4.5,
        reviewCount: 10,
        isActive: true,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BusinessCard(
              business: business,
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(BusinessCard));
      expect(tapped, true);
    });
  });
}
```

## Performance Optimization

### Image Caching
```dart
class CachedImageWidget extends StatelessWidget {
  final String? imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const CachedImageWidget({
    Key? key,
    this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return errorWidget ?? Icon(Icons.image_not_supported);
    }

    return CachedNetworkImage(
      imageUrl: imageUrl!,
      width: width,
      height: height,
      fit: fit,
      placeholder: (context, url) =>
          placeholder ?? Center(child: CircularProgressIndicator()),
      errorWidget: (context, url, error) =>
          errorWidget ?? Icon(Icons.error),
      memCacheWidth: width?.toInt(),
      memCacheHeight: height?.toInt(),
    );
  }
}
```

### List Performance
```dart
class BusinessListView extends StatelessWidget {
  final List<Business> businesses;
  final Function(Business) onBusinessTap;
  final VoidCallback? onLoadMore;
  final bool isLoading;

  const BusinessListView({
    Key? key,
    required this.businesses,
    required this.onBusinessTap,
    this.onLoadMore,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: businesses.length + (isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == businesses.length) {
          return Center(child: CircularProgressIndicator());
        }

        final business = businesses[index];

        // Load more when reaching near the end
        if (index == businesses.length - 3 && onLoadMore != null) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            onLoadMore!();
          });
        }

        return BusinessCard(
          key: ValueKey(business.id),
          business: business,
          onTap: () => onBusinessTap(business),
        );
      },
    );
  }
}
```

## Accessibility Features

### Screen Reader Support
```dart
class AccessibleBusinessCard extends StatelessWidget {
  final Business business;
  final VoidCallback? onTap;

  const AccessibleBusinessCard({
    Key? key,
    required this.business,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: _buildSemanticLabel(),
      button: true,
      onTap: onTap,
      child: BusinessCard(
        business: business,
        onTap: onTap,
      ),
    );
  }

  String _buildSemanticLabel() {
    final buffer = StringBuffer();
    buffer.write('${business.name}, ');
    buffer.write('${business.averageRating.toStringAsFixed(1)} stars, ');
    buffer.write('${business.reviewCount} reviews, ');
    buffer.write('${business.address}, ');

    if (business.distanceKm != null) {
      buffer.write('${business.distanceKm!.toStringAsFixed(1)} kilometers away, ');
    }

    buffer.write(business.isOpenNow ? 'Currently open' : 'Currently closed');

    return buffer.toString();
  }
}
```

## Deployment Considerations

### App Configuration
```dart
class AppConfig {
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://127.0.0.1:8000/api/v1',
  );

  static const String googleMapsApiKey = String.fromEnvironment(
    'GOOGLE_MAPS_API_KEY',
    defaultValue: '',
  );

  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: false,
  );
}
```

### Error Tracking
```dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp();

  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };

  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  runApp(MyApp());
}
```

This completes the comprehensive user-facing application implementation guide with modern UI/UX specifications, detailed API integration, robust state management, and production-ready considerations.
