<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BusinessFilter
{
    protected Request $request;
    protected Builder $builder;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function apply(Builder $builder): Builder
    {
        $this->builder = $builder;

        foreach ($this->filters() as $name => $value) {
            if (method_exists($this, $name) && $value !== null) {
                $this->$name($value);
            }
        }

        return $this->builder;
    }

    protected function filters(): array
    {
        return $this->request->only([
            'search',
            'category',
            'rating',
            'lat',
            'lng',
            'radius',
            'price_range',
            'open_now',
            'has_products',
            'has_services'
        ]);
    }

    protected function search(string $value): void
    {
        $this->builder->where(function ($query) use ($value) {
            $query->where('name', 'like', "%{$value}%")
                  ->orWhere('description', 'like', "%{$value}%")
                  ->orWhereHas('products', function ($q) use ($value) {
                      $q->where('name', 'like', "%{$value}%")
                        ->orWhere('description', 'like', "%{$value}%");
                  })
                  ->orWhereHas('services', function ($q) use ($value) {
                      $q->where('name', 'like', "%{$value}%")
                        ->orWhere('description', 'like', "%{$value}%");
                  });
        });
    }

    protected function category(string $value): void
    {
        $this->builder->whereHas('categories', function ($query) use ($value) {
            $query->where('slug', $value);
        });
    }

    protected function rating(int $value): void
    {
        $this->builder->where('average_rating', '>=', $value);
    }

    protected function lat(float $lat): void
    {
        $lng = $this->request->get('lng');

        if ($lng !== null) {
            $this->addDistanceCalculation($lat, $lng);
        }
    }

    protected function lng(float $lng): void
    {
        // This is handled in the lat method
    }

    protected function radius(float $radius): void
    {
        $lat = $this->request->get('lat');
        $lng = $this->request->get('lng');

        if ($lat !== null && $lng !== null) {
            $this->builder->whereRaw(
                '(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) <= ?',
                [$lat, $lng, $lat, $radius]
            );
        }
    }

    protected function priceRange(string $range): void
    {
        [$min, $max] = explode('-', $range);

        $this->builder->where(function ($query) use ($min, $max) {
            $query->whereHas('products', function ($q) use ($min, $max) {
                $q->whereBetween('price', [$min, $max]);
            })->orWhereHas('services', function ($q) use ($min, $max) {
                $q->where(function ($sq) use ($min, $max) {
                    $sq->whereBetween('price_from', [$min, $max])
                      ->orWhereBetween('price_to', [$min, $max]);
                });
            });
        });
    }

    protected function openNow(bool $openNow): void
    {
        if ($openNow) {
            $currentDay = now()->dayOfWeek === 0 ? 7 : now()->dayOfWeek; // Convert Sunday from 0 to 7
            $currentTime = now()->format('H:i');

            $this->builder->whereHas('hours', function ($query) use ($currentDay, $currentTime) {
                $query->where('day_of_week', $currentDay)
                      ->where('is_closed', false)
                      ->where('open_time', '<=', $currentTime)
                      ->where('close_time', '>=', $currentTime);
            });
        }
    }

    protected function hasProducts(bool $hasProducts): void
    {
        if ($hasProducts) {
            $this->builder->whereHas('products', function ($query) {
                $query->where('is_available', true);
            });
        }
    }

    protected function hasServices(bool $hasServices): void
    {
        if ($hasServices) {
            $this->builder->whereHas('services', function ($query) {
                $query->where('is_available', true);
            });
        }
    }

    protected function addDistanceCalculation(float $lat, float $lng): void
    {
        $this->builder->select('businesses.*')
            ->selectRaw(
                '(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance_km',
                [$lat, $lng, $lat]
            )
            ->orderBy('distance_km');
    }
}
