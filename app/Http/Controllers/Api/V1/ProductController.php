<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProductResource;
use App\Models\Business;
use App\Models\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Get products for a specific business owned by the user.
     */
    public function index(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $products = $business->products()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(20);

        return response()->json([
            'data' => ProductResource::collection($products),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ]
        ]);
    }

    /**
     * Create a new product for a business.
     */
    public function store(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
            'category' => ['nullable', 'string', 'max:100'],
            'image_url' => ['nullable', 'url'],
            'is_available' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $product = Product::create([
            'business_id' => $business->id,
            'name' => $request->name,
            'description' => $request->description,
            'price' => $request->price,
            'category' => $request->category,
            'image_url' => $request->image_url,
            'is_available' => $request->boolean('is_available', true),
            'sort_order' => $request->integer('sort_order', 0),
        ]);

        return response()->json([
            'data' => new ProductResource($product)
        ], 201);
    }

    /**
     * Get a specific product.
     */
    public function show(Request $request, Business $business, Product $product): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Ensure the product belongs to the business
        if ($product->business_id !== $business->id) {
            return response()->json([
                'message' => 'Product not found'
            ], 404);
        }

        return response()->json([
            'data' => new ProductResource($product)
        ]);
    }

    /**
     * Update a product.
     */
    public function update(Request $request, Business $business, Product $product): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Ensure the product belongs to the business
        if ($product->business_id !== $business->id) {
            return response()->json([
                'message' => 'Product not found'
            ], 404);
        }

        $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['sometimes', 'string'],
            'price' => ['sometimes', 'numeric', 'min:0'],
            'category' => ['nullable', 'string', 'max:100'],
            'image_url' => ['nullable', 'url'],
            'is_available' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $product->update($request->only([
            'name', 'description', 'price', 'category',
            'image_url', 'is_available', 'sort_order'
        ]));

        return response()->json([
            'data' => new ProductResource($product)
        ]);
    }

    /**
     * Delete a product.
     */
    public function destroy(Request $request, Business $business, Product $product): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Ensure the product belongs to the business
        if ($product->business_id !== $business->id) {
            return response()->json([
                'message' => 'Product not found'
            ], 404);
        }

        $product->delete();

        return response()->json(null, 204);
    }
}
