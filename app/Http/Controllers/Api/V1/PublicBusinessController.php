<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProductResource;
use App\Http\Resources\ServiceResource;
use App\Models\Business;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PublicBusinessController extends Controller
{
    /**
     * Get products for a business (public endpoint).
     */
    public function products(Request $request, Business $business): JsonResponse
    {
        if (!$business->is_active) {
            return response()->json([
                'message' => 'Business not found'
            ], 404);
        }

        $products = $business->products()
            ->where('is_available', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(20);

        return response()->json([
            'data' => ProductResource::collection($products),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ]
        ]);
    }

    /**
     * Get services for a business (public endpoint).
     */
    public function services(Request $request, Business $business): JsonResponse
    {
        if (!$business->is_active) {
            return response()->json([
                'message' => 'Business not found'
            ], 404);
        }

        $services = $business->services()
            ->where('is_available', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(20);

        return response()->json([
            'data' => ServiceResource::collection($services),
            'meta' => [
                'current_page' => $services->currentPage(),
                'last_page' => $services->lastPage(),
                'per_page' => $services->perPage(),
                'total' => $services->total(),
            ]
        ]);
    }

    /**
     * Get both products and services for a business.
     */
    public function offerings(Request $request, Business $business): JsonResponse
    {
        if (!$business->is_active) {
            return response()->json([
                'message' => 'Business not found'
            ], 404);
        }

        $products = $business->products()
            ->where('is_available', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        $services = $business->services()
            ->where('is_available', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        return response()->json([
            'data' => [
                'products' => ProductResource::collection($products),
                'services' => ServiceResource::collection($services),
            ]
        ]);
    }
}
