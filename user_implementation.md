# User-Facing Application - Complete Implementation Guide

## Overview
This document provides a comprehensive implementation guide for the User-Facing mobile application that allows users to discover nearby businesses, search by various criteria, view detailed business profiles, and manage favorites through the Local Directory API.

## API Base Information
- **Base URL**: `http://127.0.0.1:8000/api/v1`
- **Authentication**: <PERSON><PERSON> (Bearer Token) - Optional for some features
- **Response Format**: JSON with `data` wrapper
- **Database**: MySQL (local_directory_api)

## Design System & UI/UX Specifications

### Color Palette
```
Primary Colors:
- Primary Purple: #7C3AED (rgb(124, 58, 237))
- Primary Dark: #5B21B6 (rgb(91, 33, 182))
- Primary Light: #EDE9FE (rgb(237, 233, 254))

Secondary Colors:
- Success Green: #10B981 (rgb(16, 185, 129))
- Warning Orange: #F59E0B (rgb(245, 158, 11))
- Error Red: #EF4444 (rgb(239, 68, 68))
- Info Blue: #3B82F6 (rgb(59, 130, 246))

Accent Colors:
- Gold Star: #FCD34D (rgb(252, 211, 77))
- Heart Red: #F87171 (rgb(248, 113, 113))
- Distance Blue: #60A5FA (rgb(96, 165, 250))

Neutral Colors:
- Gray 900: #111827 (rgb(17, 24, 39)) - Primary Text
- Gray 700: #374151 (rgb(55, 65, 81)) - Secondary Text
- Gray 500: #6B7280 (rgb(107, 114, 128)) - Placeholder Text
- Gray 300: #D1D5DB (rgb(209, 213, 219)) - Borders
- Gray 100: #F3F4F6 (rgb(243, 244, 246)) - Background
- White: #FFFFFF (rgb(255, 255, 255))
```

### Typography
```
Font Family: Inter (fallback: system-ui, sans-serif)

Heading Styles:
- H1: 28px, Bold (700), Line Height 1.2
- H2: 24px, Bold (700), Line Height 1.3
- H3: 20px, SemiBold (600), Line Height 1.4
- H4: 18px, SemiBold (600), Line Height 1.4
- H5: 16px, Medium (500), Line Height 1.5

Body Styles:
- Body Large: 16px, Regular (400), Line Height 1.6
- Body Medium: 14px, Regular (400), Line Height 1.5
- Body Small: 12px, Regular (400), Line Height 1.4
- Caption: 10px, Regular (400), Line Height 1.3

Button Text: 14px, SemiBold (600), Line Height 1
```

### Spacing System
```
Base Unit: 4px

Spacing Scale:
- xs: 4px
- sm: 8px
- md: 12px
- lg: 16px
- xl: 20px
- 2xl: 24px
- 3xl: 32px

Component Spacing:
- Screen Padding: 16px (lg)
- Card Padding: 12px (md)
- Section Spacing: 20px (xl)
- Element Spacing: 8px (sm)
```

### Component Specifications

#### Search Bar
```
Search Input:
- Background: White
- Border: 1px solid Gray 300
- Border Radius: 24px
- Height: 44px
- Padding: 12px 16px 12px 44px
- Font: 16px Regular
- Placeholder: Gray 500
- Search Icon: 20px, Gray 500, positioned left

Focus State:
- Border: 2px solid Primary Purple
- Shadow: 0 0 0 3px rgba(124, 58, 237, 0.1)
```

#### Filter Chips
```
Filter Chip:
- Background: Gray 100
- Border: 1px solid Gray 300
- Border Radius: 20px
- Height: 32px
- Padding: 6px 12px
- Font: 12px Medium
- Text: Gray 700

Active Filter Chip:
- Background: Primary Purple
- Border: 1px solid Primary Purple
- Text: White

Filter Chip with Count:
- Badge: Primary Purple background, White text
- Position: Top right corner
```

#### Business Cards
```
List View Card:
- Background: White
- Border Radius: 12px
- Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
- Padding: 12px
- Margin: 8px horizontal, 4px vertical

Grid View Card:
- Background: White
- Border Radius: 12px
- Shadow: 0 1px 3px rgba(0, 0, 0, 0.1)
- Aspect Ratio: 1:1.2
- Padding: 8px
```

## Screen-by-Screen Implementation

### 1. Onboarding & Authentication

#### 1.1 Welcome Screen
**Layout:**
- Full-screen illustration
- App logo and tagline
- Location permission request
- Get started button

**Features:**
- Animated illustrations
- Location services setup
- Optional user registration

#### 1.2 Location Permission Screen
**Layout:**
- Location icon and explanation
- Benefits of location access
- Allow/Skip buttons

**Implementation:**
```dart
Future<void> requestLocationPermission() async {
  final permission = await Geolocator.requestPermission();
  if (permission == LocationPermission.always || 
      permission == LocationPermission.whileInUse) {
    await getCurrentLocation();
  }
}
```

### 2. Home & Discovery

#### 2.1 Home Screen
**Layout:**
- Search bar at top
- Category chips (horizontal scroll)
- "Near You" section with business cards
- "Popular" section
- "Recently Viewed" section (if logged in)

**API Endpoints:**
- `GET /categories` - Category list
- `GET /businesses?lat={lat}&lng={lng}` - Nearby businesses

**UI Components:**
```
┌─────────────────────────────────────┐
│ 🔍 Search businesses...            │
├─────────────────────────────────────┤
│ [🍕] [🛒] [⚡] [🏥] [🎬] [➡️]      │
├─────────────────────────────────────┤
│ Near You (2.1 km)                  │
│ ┌─────────────────────────────────┐ │
│ │ [📷] Pizza Palace        ❤️    │ │
│ │      ⭐ 4.5 • $$ • 0.8km      │ │
│ │      Open until 11 PM          │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 2.2 Search Results Screen
**Layout:**
- Search query display
- Filter button and active filters
- Sort options (Distance, Rating, Price)
- List/Grid view toggle
- Business results with pagination

**API Endpoint:** 
```
GET /businesses?search={query}&category={category}&rating={rating}&lat={lat}&lng={lng}&radius={radius}&price_range={range}&open_now={bool}&page={page}
```

**Filter Options:**
- Categories (multi-select)
- Rating (1-5 stars)
- Distance (1km, 5km, 10km, 25km)
- Price Range ($, $$, $$$, $$$$)
- Open Now (toggle)
- Has Products (toggle)
- Has Services (toggle)

#### 2.3 Map View Screen
**Layout:**
- Full-screen map
- Business markers with clustering
- Current location indicator
- Search bar overlay
- Filter button
- List view toggle

**Implementation:**
```dart
class MapScreen extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: CameraPosition(
              target: _currentLocation,
              zoom: 14.0,
            ),
            markers: _businessMarkers,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
          ),
          _buildSearchOverlay(),
          _buildFilterButton(),
          _buildListToggle(),
        ],
      ),
    );
  }
}
```

### 3. Business Discovery & Details

#### 3.1 Business Detail Screen
**Layout:**
- Hero image/gallery
- Business name and rating
- Quick actions (Call, Website, Directions, Favorite)
- Business information tabs
- Reviews section
- Products/Services section

**API Endpoints:**
- `GET /businesses/{slug}` - Business details
- `GET /businesses/{id}/products` - Business products
- `GET /businesses/{id}/services` - Business services
- `GET /businesses/{id}/offerings` - Combined products/services

**Screen Sections:**

**Hero Section:**
```
┌─────────────────────────────────────┐
│ [📷 Business Photo Gallery]        │
│                              [❤️]   │
├─────────────────────────────────────┤
│ Pizza Palace                        │
│ ⭐⭐⭐⭐⭐ 4.8 (127 reviews)        │
│ 📍 0.8 km • $$ • Italian           │
│ 🕒 Open until 11:00 PM             │
├─────────────────────────────────────┤
│ [📞] [🌐] [🗺️] [❤️]               │
└─────────────────────────────────────┘
```

**Information Tabs:**
- Overview (Description, Hours, Contact)
- Menu/Products (if available)
- Services (if available)
- Reviews
- Photos

#### 3.2 Business Hours Display
**Layout:**
- Current day highlighted
- Open/Closed status
- All week schedule
- Special hours notes

**Implementation:**
```dart
class BusinessHoursWidget extends StatelessWidget {
  final List<BusinessHour> hours;
  
  @override
  Widget build(BuildContext context) {
    final today = DateTime.now().weekday;
    
    return Column(
      children: hours.map((hour) {
        final isToday = hour.dayOfWeek == today;
        return Container(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: isToday ? Colors.purple.withOpacity(0.1) : null,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getDayName(hour.dayOfWeek),
                style: TextStyle(
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              Text(
                hour.isClosed 
                    ? 'Closed' 
                    : '${hour.openTime} - ${hour.closeTime}',
                style: TextStyle(
                  color: hour.isClosed ? Colors.red : Colors.green,
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
```

#### 3.3 Products/Services Display
**Layout:**
- Category tabs (if multiple categories)
- Grid or list view of items
- Item cards with image, name, price
- Quick view modal for details

**Product/Service Card:**
```
┌─────────────────────────────────────┐
│ [📷] Margherita Pizza               │
│      Fresh tomatoes, mozzarella...  │
│      $18.99                         │
│      ⭐ 4.7 (23 reviews)            │
└─────────────────────────────────────┘
```

### 4. Reviews & Ratings

#### 4.1 Reviews List Screen
**Layout:**
- Overall rating summary
- Rating distribution chart
- Sort options (Newest, Oldest, Highest, Lowest)
- Review cards with user info

**API Endpoint:** `GET /businesses/{id}/reviews` (Note: This endpoint needs to be created)

**Review Card Design:**
```
┌─────────────────────────────────────┐
│ [👤] John D. • ⭐⭐⭐⭐⭐ • 2 days │
│ "Amazing pizza and great service!   │
│ The staff was very friendly and..." │
│                                     │
│ 👍 12 people found this helpful     │
│ ─────────────────────────────────   │
│ 💬 Owner replied: "Thank you..."    │
└─────────────────────────────────────┘
```

#### 4.2 Write Review Screen (Authenticated Users)
**Layout:**
- Business info header
- Star rating selector
- Review text area
- Photo upload option
- Submit button

**API Endpoint:** `POST /businesses/{id}/reviews`

**Implementation:**
```dart
class WriteReviewScreen extends StatefulWidget {
  final Business business;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Write Review')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            _buildBusinessHeader(),
            SizedBox(height: 24),
            _buildRatingSelector(),
            SizedBox(height: 16),
            _buildReviewTextField(),
            SizedBox(height: 16),
            _buildPhotoUpload(),
            Spacer(),
            _buildSubmitButton(),
          ],
        ),
      ),
    );
  }
}
```

### 5. Favorites & User Profile

#### 5.1 Favorites Screen
**Layout:**
- Search within favorites
- Sort options (Recently Added, Distance, Rating)
- Favorite business cards
- Empty state for no favorites

**API Endpoint:** `GET /me/favorites`

**Features:**
- Swipe to remove from favorites
- Quick actions (Call, Directions)
- Offline access to basic info

#### 5.2 User Profile Screen
**Layout:**
- User avatar and basic info
- Account settings
- App preferences
- Support options

**API Endpoint:** `GET /me`

**Profile Options:**
- Edit profile
- Notification settings
- Privacy preferences
- Location settings
- Help & support
- About the app

### 6. Search & Filtering

#### 6.1 Advanced Search Screen
**Layout:**
- Search input with suggestions
- Recent searches
- Popular searches
- Search by category

**Search Suggestions Implementation:**
```dart
class SearchSuggestions extends StatelessWidget {
  final String query;
  final Function(String) onSuggestionTap;
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<String>>(
      future: _getSearchSuggestions(query),
      builder: (context, snapshot) {
        if (!snapshot.hasData) return CircularProgressIndicator();
        
        return ListView.builder(
          itemCount: snapshot.data!.length,
          itemBuilder: (context, index) {
            final suggestion = snapshot.data![index];
            return ListTile(
              leading: Icon(Icons.search),
              title: Text(suggestion),
              onTap: () => onSuggestionTap(suggestion),
            );
          },
        );
      },
    );
  }
}
```

#### 6.2 Filter Screen
**Layout:**
- Filter categories with expandable sections
- Range sliders for distance and price
- Toggle switches for boolean filters
- Apply/Clear buttons

**Filter Categories:**
- Business Categories
- Distance Range
- Price Range
- Rating Minimum
- Open Now
- Has Products/Services

### 7. Navigation & Bottom Tabs

#### 7.1 Bottom Navigation
**Tabs:**
1. **Discover** (Home icon) - Home screen
2. **Search** (Search icon) - Search screen
3. **Map** (Map icon) - Map view
4. **Favorites** (Heart icon) - Favorites list
5. **Profile** (User icon) - Profile screen

#### 7.2 Navigation Implementation
```dart
class MainNavigationScreen extends StatefulWidget {
  @override
  _MainNavigationScreenState createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;
  
  final List<Widget> _screens = [
    DiscoverScreen(),
    SearchScreen(),
    MapScreen(),
    FavoritesScreen(),
    ProfileScreen(),
  ];
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.explore),
            label: 'Discover',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.map),
            label: 'Map',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite),
            label: 'Favorites',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}
```
