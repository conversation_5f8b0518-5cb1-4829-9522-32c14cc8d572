<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\ServiceResource;
use App\Models\Business;
use App\Models\Service;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    /**
     * Get services for a specific business owned by the user.
     */
    public function index(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $services = $business->services()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(20);

        return response()->json([
            'data' => ServiceResource::collection($services),
            'meta' => [
                'current_page' => $services->currentPage(),
                'last_page' => $services->lastPage(),
                'per_page' => $services->perPage(),
                'total' => $services->total(),
            ]
        ]);
    }

    /**
     * Create a new service for a business.
     */
    public function store(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'price_from' => ['nullable', 'numeric', 'min:0'],
            'price_to' => ['nullable', 'numeric', 'min:0', 'gte:price_from'],
            'duration_minutes' => ['nullable', 'integer', 'min:1'],
            'category' => ['nullable', 'string', 'max:100'],
            'image_url' => ['nullable', 'url'],
            'is_available' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $service = Service::create([
            'business_id' => $business->id,
            'name' => $request->name,
            'description' => $request->description,
            'price_from' => $request->price_from,
            'price_to' => $request->price_to,
            'duration_minutes' => $request->duration_minutes,
            'category' => $request->category,
            'image_url' => $request->image_url,
            'is_available' => $request->boolean('is_available', true),
            'sort_order' => $request->integer('sort_order', 0),
        ]);

        return response()->json([
            'data' => new ServiceResource($service)
        ], 201);
    }

    /**
     * Get a specific service.
     */
    public function show(Request $request, Business $business, Service $service): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Ensure the service belongs to the business
        if ($service->business_id !== $business->id) {
            return response()->json([
                'message' => 'Service not found'
            ], 404);
        }

        return response()->json([
            'data' => new ServiceResource($service)
        ]);
    }

    /**
     * Update a service.
     */
    public function update(Request $request, Business $business, Service $service): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Ensure the service belongs to the business
        if ($service->business_id !== $business->id) {
            return response()->json([
                'message' => 'Service not found'
            ], 404);
        }

        $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['sometimes', 'string'],
            'price_from' => ['nullable', 'numeric', 'min:0'],
            'price_to' => ['nullable', 'numeric', 'min:0', 'gte:price_from'],
            'duration_minutes' => ['nullable', 'integer', 'min:1'],
            'category' => ['nullable', 'string', 'max:100'],
            'image_url' => ['nullable', 'url'],
            'is_available' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $service->update($request->only([
            'name', 'description', 'price_from', 'price_to',
            'duration_minutes', 'category', 'image_url',
            'is_available', 'sort_order'
        ]));

        return response()->json([
            'data' => new ServiceResource($service)
        ]);
    }

    /**
     * Delete a service.
     */
    public function destroy(Request $request, Business $business, Service $service): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        // Ensure the service belongs to the business
        if ($service->business_id !== $business->id) {
            return response()->json([
                'message' => 'Service not found'
            ], 404);
        }

        $service->delete();

        return response()->json(null, 204);
    }
}
