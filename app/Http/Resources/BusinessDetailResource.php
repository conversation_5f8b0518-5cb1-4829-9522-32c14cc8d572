<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BusinessDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return array_merge((new BusinessSummaryResource($this))->toArray($request), [
            'description' => $this->description,
            'phone_number' => $this->phone_number,
            'website_url' => $this->website_url,
            'categories' => CategoryResource::collection($this->whenLoaded('categories')),
            'hours' => BusinessHourResource::collection($this->whenLoaded('hours')),
            'reviews' => ReviewResource::collection($this->whenLoaded('reviews')),
            'media' => BusinessMediaResource::collection($this->whenLoaded('media')),
            'products' => ProductResource::collection($this->whenLoaded('products')),
            'services' => ServiceResource::collection($this->whenLoaded('services')),
        ]);
    }
}
