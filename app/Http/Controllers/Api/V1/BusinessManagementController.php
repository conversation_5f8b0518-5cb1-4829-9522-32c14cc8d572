<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessDetailResource;
use App\Http\Resources\BusinessSummaryResource;
use App\Models\Business;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BusinessManagementController extends Controller
{
    /**
     * Get all businesses owned by the authenticated user.
     */
    public function index(Request $request): JsonResponse
    {
        $businesses = $request->user()
            ->businesses()
            ->with(['categories', 'hours'])
            ->paginate(15);

        return response()->json([
            'data' => BusinessSummaryResource::collection($businesses),
            'meta' => [
                'current_page' => $businesses->currentPage(),
                'last_page' => $businesses->lastPage(),
                'per_page' => $businesses->perPage(),
                'total' => $businesses->total(),
            ]
        ]);
    }

    /**
     * Create a new business.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string'],
            'phone_number' => ['required', 'string', 'max:20'],
            'website_url' => ['nullable', 'url'],
            'address' => ['required', 'string'],
            'latitude' => ['required', 'numeric', 'between:-90,90'],
            'longitude' => ['required', 'numeric', 'between:-180,180'],
            'logo_url' => ['nullable', 'url'],
            'category_ids' => ['required', 'array'],
            'category_ids.*' => ['exists:categories,id'],
        ]);

        DB::beginTransaction();
        try {
            $business = Business::create([
                'user_id' => $request->user()->id,
                'name' => $request->name,
                'description' => $request->description,
                'phone_number' => $request->phone_number,
                'website_url' => $request->website_url,
                'address' => $request->address,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'logo_url' => $request->logo_url,
                'is_active' => false, // Requires approval
            ]);

            $business->categories()->attach($request->category_ids);

            DB::commit();

            $business->load(['categories', 'hours']);

            return response()->json([
                'data' => new BusinessDetailResource($business)
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to create business'
            ], 500);
        }
    }

    /**
     * Get a specific business owned by the user.
     */
    public function show(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $business->load([
            'categories',
            'hours',
            'reviews.user',
            'media',
            'products',
            'services'
        ]);

        return response()->json([
            'data' => new BusinessDetailResource($business)
        ]);
    }

    /**
     * Update a business.
     */
    public function update(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'description' => ['sometimes', 'string'],
            'phone_number' => ['sometimes', 'string', 'max:20'],
            'website_url' => ['nullable', 'url'],
            'address' => ['sometimes', 'string'],
            'latitude' => ['sometimes', 'numeric', 'between:-90,90'],
            'longitude' => ['sometimes', 'numeric', 'between:-180,180'],
            'logo_url' => ['nullable', 'url'],
            'category_ids' => ['sometimes', 'array'],
            'category_ids.*' => ['exists:categories,id'],
        ]);

        DB::beginTransaction();
        try {
            $business->update($request->only([
                'name', 'description', 'phone_number', 'website_url',
                'address', 'latitude', 'longitude', 'logo_url'
            ]));

            if ($request->has('category_ids')) {
                $business->categories()->sync($request->category_ids);
            }

            DB::commit();

            $business->load(['categories', 'hours']);

            return response()->json([
                'data' => new BusinessDetailResource($business)
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to update business'
            ], 500);
        }
    }

    /**
     * Delete a business.
     */
    public function destroy(Request $request, Business $business): JsonResponse
    {
        // Ensure the business belongs to the authenticated user
        if ($business->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $business->delete();

        return response()->json(null, 204);
    }
}
