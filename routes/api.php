<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\BusinessController;
use App\Http\Controllers\Api\V1\BusinessAnalyticsController;
use App\Http\Controllers\Api\V1\BusinessHoursController;
use App\Http\Controllers\Api\V1\BusinessManagementController;
use App\Http\Controllers\Api\V1\CategoryController;
use App\Http\Controllers\Api\V1\FavoriteController;
use App\Http\Controllers\Api\V1\MyBusinessController;
use App\Http\Controllers\Api\V1\MyBusinessMediaController;
use App\Http\Controllers\Api\V1\ProductController;
use App\Http\Controllers\Api\V1\ProfileController;
use App\Http\Controllers\Api\V1\PublicBusinessController;
use App\Http\Controllers\Api\V1\ReviewController;
use App\Http\Controllers\Api\V1\ServiceController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::prefix('v1')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);

    // Public endpoints
    Route::get('/categories', [CategoryController::class, 'index']);
    Route::get('/businesses', [BusinessController::class, 'index']);
    Route::get('/businesses/{business:slug}', [BusinessController::class, 'show']);
    Route::get('/businesses/{business}/products', [PublicBusinessController::class, 'products']);
    Route::get('/businesses/{business}/services', [PublicBusinessController::class, 'services']);
    Route::get('/businesses/{business}/offerings', [PublicBusinessController::class, 'offerings']);

    // Authenticated routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);

        // Profile
        Route::get('/me', [ProfileController::class, 'show']);

        // Reviews
        Route::post('/businesses/{business}/reviews', [ReviewController::class, 'store']);

        // Favorites
        Route::post('/businesses/{business}/favorite', [FavoriteController::class, 'toggle']);
        Route::get('/me/favorites', [FavoriteController::class, 'index']);

        // Business owner routes
        Route::post('/my-business/reviews/{review}/reply', [MyBusinessController::class, 'replyToReview']);

        // Business media upload
        Route::post('/my-business/media', [MyBusinessMediaController::class, 'store']);

        // Business Management
        Route::apiResource('my-businesses', BusinessManagementController::class);

        // Business Hours Management
        Route::get('/my-businesses/{business}/hours', [BusinessHoursController::class, 'index']);
        Route::put('/my-businesses/{business}/hours', [BusinessHoursController::class, 'update']);
        Route::put('/my-businesses/{business}/hours/{dayOfWeek}', [BusinessHoursController::class, 'updateDay']);

        // Products Management
        Route::apiResource('my-businesses.products', ProductController::class);

        // Services Management
        Route::apiResource('my-businesses.services', ServiceController::class);

        // Business Analytics
        Route::get('/my-businesses/{business}/analytics/overview', [BusinessAnalyticsController::class, 'overview']);
        Route::get('/my-businesses/{business}/analytics/reviews', [BusinessAnalyticsController::class, 'reviews']);
        Route::get('/my-businesses/{business}/analytics/engagement', [BusinessAnalyticsController::class, 'engagement']);
        Route::get('/my-businesses/{business}/analytics/offerings', [BusinessAnalyticsController::class, 'offerings']);
    });
});
